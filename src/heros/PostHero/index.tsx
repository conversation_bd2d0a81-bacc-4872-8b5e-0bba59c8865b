import { formatDateTime } from 'src/utilities/formatDateTime'
import React from 'react'

import type { Post } from '@/payload-types'

import { PostImage } from '@/components/PostImage'
import { formatAuthors } from '@/utilities/formatAuthors'

export const PostHero: React.FC<{
  post: Post
}> = ({ post }) => {
  const { categories, featuredImage, populatedAuthors, publishedAt, title, summary } = post

  const hasAuthors =
    populatedAuthors && populatedAuthors.length > 0 && formatAuthors(populatedAuthors) !== ''

  return (
    <div className="container mx-auto px-4 py-16">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-16 items-center">
        {/* Left side - Content */}
        <div className="order-2 lg:order-1">
          {/* Categories */}
          {categories && categories.length > 0 && (
            <div className="uppercase text-sm mb-6 text-gray-600 font-medium tracking-wider">
              {categories.map((category, index) => {
                if (typeof category === 'object' && category !== null) {
                  const { title: categoryTitle } = category
                  const titleToUse = categoryTitle || 'Untitled category'
                  const isLast = index === categories.length - 1

                  return (
                    <React.Fragment key={index}>
                      {titleToUse}
                      {!isLast && <React.Fragment>, &nbsp;</React.Fragment>}
                    </React.Fragment>
                  )
                }
                return null
              })}
            </div>
          )}

          {/* Title */}
          <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6 leading-tight text-gray-900">
            {title}
          </h1>

          {/* Summary */}
          {summary && <p className="text-lg text-gray-700 mb-8 leading-relaxed">{summary}</p>}

          {/* Author and Date */}
          <div className="flex flex-col sm:flex-row gap-4 sm:gap-8 text-sm text-gray-600">
            {hasAuthors && (
              <div className="flex flex-col gap-1">
                <p className="font-medium uppercase tracking-wider">Author</p>
                <p className="text-gray-900">{formatAuthors(populatedAuthors)}</p>
              </div>
            )}
            {publishedAt && (
              <div className="flex flex-col gap-1">
                <p className="font-medium uppercase tracking-wider">Date Published</p>
                <time dateTime={publishedAt} className="text-gray-900">
                  {formatDateTime(publishedAt)}
                </time>
              </div>
            )}
          </div>
        </div>

        {/* Right side - Image */}
        <div className="order-1 lg:order-2">
          {featuredImage ? (
            <div className="relative aspect-[4/3] w-full rounded-lg overflow-hidden shadow-lg">
              <PostImage
                image={featuredImage}
                fill
                priority
                imgClassName="object-cover"
                alt={title}
              />
            </div>
          ) : (
            <div className="relative aspect-[4/3] w-full rounded-lg overflow-hidden bg-gray-200 flex items-center justify-center">
              <p className="text-gray-500">No featured image</p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
