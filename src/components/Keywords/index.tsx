import React from 'react'
import type { Keyword } from '@/payload-types'

interface KeywordsProps {
  keywords?: (number | Keyword)[] | null
  className?: string
}

export const Keywords: React.FC<KeywordsProps> = ({ keywords, className = '' }) => {
  if (!keywords || keywords.length === 0) {
    return null
  }

  return (
    <div className={`flex flex-wrap gap-2 ${className}`}>
      {keywords.map((keyword, index) => {
        if (typeof keyword === 'object' && keyword !== null) {
          return (
            <span
              key={keyword.id || index}
              className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800 hover:bg-gray-200 transition-colors"
            >
              {keyword.name}
            </span>
          )
        }
        return null
      })}
    </div>
  )
}
