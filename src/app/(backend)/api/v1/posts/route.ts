import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { getPayload } from 'payload'
import { convertMarkdownToLexical, editorConfigFactory } from '@payloadcms/richtext-lexical'
import configPromise from '@/payload.config'
import { Post } from '@/payload-types'
import {
  uploadToBlob,
  generateUniqueFilename,
  getContentTypeFromFilename,
  getExtensionFromBase64,
  base64ToBuffer,
} from '@/lib/azure-blob'

// Hardcoded auth token - replace with environment variable in production
const AUTH_TOKEN = 'press-site-api-token-12345'

const payload = await getPayload({ config: configPromise })

// Schema for category input - can be the title or an object with title and optional slug
const CategoryInputSchema = z.union([
  z.string(), // category title
  z.object({
    title: z.string(),
    slug: z.string().optional(),
  }),
])

// Schema for individual post data
const PostSchema = z.object({
  title: z.string(),
  slug: z.string(),
  content: z.string(), // Markdown content
  summary: z.string().optional(),
  source_url: z.string().url().optional(),
  source_site_name: z.string().optional(),
  extra: z.record(z.any()).optional(),
  categories: z.array(CategoryInputSchema).optional(),
  keywords: z.array(z.string()).optional(),
  meta: z
    .object({
      title: z.string().optional(),
      description: z.string().optional(),
    })
    .optional(),
  publishedAt: z.string().datetime().optional(),
  _status: z.enum(['draft', 'published']).optional().default('draft'),
  // Image handling fields
  featuredImage: z
    .object({
      type: z.enum(['base64', 'external']),
      data: z.string(), // Base64 string or external URL
      alt: z.string().optional(),
    })
    .optional(),
})

type PostData = Pick<
  Post,
  | 'title'
  | 'slug'
  | 'content'
  | '_status'
  | 'meta'
  | 'publishedAt'
  | 'categories'
  | 'keywords'
  | 'extra'
> & {
  summary?: string
  source_url?: string
  source_site_name?: string
  featuredImage?: {
    type?: 'upload' | 'external'
    media?: number
    url?: string
    alt?: string
  }
}

// Unified request schema - handles both single post and array of posts
const RequestSchema = z.union([
  PostSchema, // Single post
  z.object({
    posts: z.array(PostSchema), // Array of posts
  }),
])

// Response schemas
const ErrorResponseSchema = z.object({
  success: z.literal(false),
  error: z.string(),
  details: z.any().optional(),
})

const SuccessPostSchema = z.object({
  id: z.number(),
  title: z.string(),
  slug: z.string(),
  success: z.literal(true),
})

const FailedPostSchema = z.object({
  title: z.string(),
  slug: z.string(),
  success: z.literal(false),
  error: z.string(),
})

// Single post response
const SinglePostResponseSchema = z.object({
  success: z.literal(true),
  post: z.object({
    id: z.number(),
    title: z.string(),
    slug: z.string(),
  }),
})

// Batch response
const BatchResponseSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  totalRequested: z.number(),
  succeeded: z.number(),
  failed: z.number(),
  results: z.array(z.union([SuccessPostSchema, FailedPostSchema])),
})

type BatchResponseType = z.infer<typeof BatchResponseSchema>

/**
 * Sanitizes a string to be used as a slug
 */
function sanitizeSlug(input: string): string {
  return input
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove non-alphanumeric characters except spaces and dashes
    .replace(/\s+/g, '-') // Replace spaces with dashes
    .replace(/-+/g, '-') // Replace multiple dashes with single dash
    .replace(/^-|-$/g, '') // Remove leading/trailing dashes
}

/**
 * Generates a random slug for categories
 */
function generateRandomSlug(): string {
  const timestamp = Date.now().toString(36)
  const randomStr = Math.random().toString(36).substring(2, 8)
  return `category-${timestamp}-${randomStr}`
}

/**
 * Generates a unique slug by appending a numeric suffix if the slug already exists
 */
async function generateUniqueSlug(baseSlug: string): Promise<string> {
  // First check if the original slug is available
  const existingPosts = await payload.find({
    collection: 'posts',
    where: {
      slug: {
        equals: baseSlug,
      },
    },
    disableErrors: true,
  })

  // If no posts with this slug, use it as is
  if (existingPosts.docs.length === 0) {
    return baseSlug
  }

  // Otherwise, find a unique variation by appending a number
  let counter = 1
  let uniqueSlug = ''
  let isUnique = false

  while (!isUnique) {
    uniqueSlug = `${baseSlug}-${counter}`

    const existingWithSuffix = await payload.find({
      collection: 'posts',
      where: {
        slug: {
          equals: uniqueSlug,
        },
      },
      disableErrors: true,
    })

    isUnique = existingWithSuffix.docs.length === 0
    if (!isUnique) counter++
  }

  return uniqueSlug
}

/**
 * Finds or creates a keyword and returns its ID
 */
async function findOrCreateKeyword(
  keywordName: string,
  keywordMap: Record<string, number>,
): Promise<number | undefined> {
  // Trim whitespace from keyword name
  const trimmedKeywordName = keywordName.trim()

  if (!trimmedKeywordName) {
    return undefined
  }

  // Check if already in our map cache by name
  if (keywordMap[trimmedKeywordName]) {
    return keywordMap[trimmedKeywordName]
  }

  // Try to find keyword in database by name
  const existingKeywords = await payload.find({
    collection: 'keywords',
    where: {
      name: {
        equals: trimmedKeywordName,
      },
    },
    disableErrors: true,
  })

  if (existingKeywords.docs.length > 0 && existingKeywords.docs[0]?.id !== undefined) {
    // Found in DB, update usage count and last_used
    const keyword = existingKeywords.docs[0]
    const keywordId = keyword.id

    try {
      await payload.update({
        collection: 'keywords',
        id: keywordId,
        data: {
          usage_count: (keyword.usage_count || 0) + 1,
          last_used: new Date().toISOString(),
        },
        overrideAccess: true,
      })
    } catch (error: any) {
      console.error(`Error updating keyword usage "${trimmedKeywordName}":`, error.message)
    }

    keywordMap[trimmedKeywordName] = Number(keywordId)
    return Number(keywordId)
  }

  // Keyword doesn't exist, create it
  try {
    const newKeyword = await payload.create({
      collection: 'keywords',
      data: {
        name: trimmedKeywordName,
        usage_count: 1,
        last_used: new Date().toISOString(),
      },
      overrideAccess: true,
    })

    if (newKeyword.id !== undefined) {
      keywordMap[trimmedKeywordName] = Number(newKeyword.id)
      return Number(newKeyword.id)
    }
  } catch (error: any) {
    console.error(`Error creating keyword "${trimmedKeywordName}":`, error.message)
  }

  return undefined
}

/**
 * Finds or creates a category and returns its ID
 */
async function findOrCreateCategory(
  categoryInput: string | { title: string; slug?: string },
  categoryMap: Record<string, number>,
): Promise<number | undefined> {
  // Handle string input (label)
  if (typeof categoryInput === 'string') {
    const categoryTitle = categoryInput

    // Check if already in our map cache by label
    if (categoryMap[categoryTitle]) {
      return categoryMap[categoryTitle]
    }

    // Try to find category in database by title (label)
    const existingCategories = await payload.find({
      collection: 'categories',
      where: {
        title: {
          equals: categoryTitle,
        },
      },
      disableErrors: true,
    })

    if (existingCategories.docs.length > 0 && existingCategories.docs[0]?.id !== undefined) {
      // Found in DB, add to cache and return
      const categoryId = existingCategories.docs[0].id
      categoryMap[categoryTitle] = Number(categoryId)
      return Number(categoryId)
    }

    // Category doesn't exist, create it with random slug
    try {
      const randomSlug = generateRandomSlug()
      const newCategory = await payload.create({
        collection: 'categories',
        data: {
          title: categoryTitle,
          slug: randomSlug,
        },
        overrideAccess: true,
      })

      if (newCategory.id !== undefined) {
        categoryMap[categoryTitle] = Number(newCategory.id)
        return Number(newCategory.id)
      }
    } catch (error: any) {
      console.error(`Error creating category "${categoryTitle}":`, error.message)
    }

    return undefined
  }

  // Handle object input ({ title, slug? })
  const categoryTitle = categoryInput.title
  const providedSlug = categoryInput.slug

  // Check if already in our map cache by title
  if (categoryMap[categoryTitle]) {
    return categoryMap[categoryTitle]
  }

  // Try to find category in database by title first
  const existingCategoriesByTitle = await payload.find({
    collection: 'categories',
    where: {
      title: {
        equals: categoryTitle,
      },
    },
    disableErrors: true,
  })

  if (
    existingCategoriesByTitle.docs.length > 0 &&
    existingCategoriesByTitle.docs[0]?.id !== undefined
  ) {
    // Found existing category with same title
    const existingCategory = existingCategoriesByTitle.docs[0]
    const categoryId = existingCategory.id

    // If a slug was provided and it's different from existing, log a warning
    if (providedSlug && existingCategory.slug !== providedSlug) {
      console.warn(
        `Category "${categoryTitle}" already exists with slug "${existingCategory.slug}", ignoring provided slug "${providedSlug}"`,
      )
    }

    categoryMap[categoryTitle] = Number(categoryId)
    return Number(categoryId)
  }

  // Category doesn't exist, create it
  try {
    let slugToUse: string

    if (providedSlug) {
      // Sanitize the provided slug
      slugToUse = sanitizeSlug(providedSlug)

      // Check if sanitized slug already exists
      const existingCategoriesBySlug = await payload.find({
        collection: 'categories',
        where: {
          slug: {
            equals: slugToUse,
          },
        },
        disableErrors: true,
      })

      // If slug already exists, generate a unique one
      if (existingCategoriesBySlug.docs.length > 0) {
        console.warn(
          `Slug "${slugToUse}" already exists, generating random slug for category "${categoryTitle}"`,
        )
        slugToUse = generateRandomSlug()
      }
    } else {
      // No slug provided, generate random one
      slugToUse = generateRandomSlug()
    }

    const newCategory = await payload.create({
      collection: 'categories',
      data: {
        title: categoryTitle,
        slug: slugToUse,
      },
      overrideAccess: true,
    })

    if (newCategory.id !== undefined) {
      categoryMap[categoryTitle] = Number(newCategory.id)
      return Number(newCategory.id)
    }
  } catch (error: any) {
    console.error(`Error creating category "${categoryTitle}":`, error.message)
  }

  return undefined
}

/**
 * Process a single post
 */
async function processSinglePost(
  post: z.infer<typeof PostSchema>,
  categoryMap: Record<string, number>,
  keywordMap: Record<string, number>,
  config: any,
): Promise<
  | { success: true; post: { id: number; title: string; slug: string } }
  | { success: false; error: string }
> {
  try {
    // Generate a unique slug for this post
    const uniqueSlug = await generateUniqueSlug(post.slug)

    // Convert markdown to Lexical
    let content
    try {
      content = convertMarkdownToLexical({
        editorConfig: await editorConfigFactory.default({
          config,
        }),
        markdown: post.content,
      })
    } catch (conversionError: any) {
      return {
        success: false,
        error: `Markdown conversion error: ${conversionError.message || 'Unknown error'}`,
      }
    }

    // Handle featured image if it exists
    let featuredImageData: PostData['featuredImage'] = undefined

    if (post.featuredImage) {
      const { type, data, alt } = post.featuredImage

      if (type === 'base64') {
        // Handle base64 image upload to Azure Blob Storage
        try {
          // Convert base64 to buffer
          const buffer = base64ToBuffer(data)

          // Extract file extension from base64 data URL
          const extension = getExtensionFromBase64(data)

          // Generate unique filename and get content type
          const uniqueFilename = generateUniqueFilename(extension, 'post-image')
          const contentType = getContentTypeFromFilename(uniqueFilename)

          // Upload to Azure Blob Storage
          const blobUrl = await uploadToBlob(buffer, uniqueFilename, contentType)

          // Create media entry in Payload with blob URL
          const media = await payload.create({
            collection: 'media',
            data: {
              alt: alt || post.title,
              url: blobUrl,
              filename: uniqueFilename,
            },
            overrideAccess: true,
          })

          featuredImageData = {
            type: 'upload',
            media: Number(media.id),
            alt: alt || post.title,
          }
        } catch (imageError: any) {
          return {
            success: false,
            error: `Image upload error: ${imageError.message || 'Unknown error'}`,
          }
        }
      } else if (type === 'external') {
        // Handle external image URL - store directly without creating media entry
        featuredImageData = {
          type: 'external',
          url: data,
          alt: alt || post.title,
        }
      }
    }

    // Prepare post data for creation
    const postData: PostData = {
      title: post.title,
      slug: uniqueSlug,
      content: content as Post['content'],
      summary: post.summary,
      source_url: post.source_url,
      source_site_name: post.source_site_name,
      extra: post.extra,
      meta: post.meta,
      _status: post._status || 'draft',
      publishedAt: post.publishedAt || new Date().toISOString(),
      featuredImage: featuredImageData,
    }

    // Process categories if they exist
    if (post.categories && post.categories.length > 0) {
      const categoryIds: number[] = []

      // Process each category - find or create them as needed
      for (const categoryInput of post.categories) {
        const categoryId = await findOrCreateCategory(categoryInput, categoryMap)
        if (categoryId !== undefined) {
          categoryIds.push(categoryId)
        }
      }

      if (categoryIds.length > 0) {
        postData.categories = categoryIds
      }
    }

    // Process keywords if they exist
    if (post.keywords && post.keywords.length > 0) {
      const keywordIds: number[] = []

      // Process each keyword - find or create them as needed
      for (const keywordName of post.keywords) {
        const keywordId = await findOrCreateKeyword(keywordName, keywordMap)
        if (keywordId !== undefined) {
          keywordIds.push(keywordId)
        }
      }

      if (keywordIds.length > 0) {
        postData.keywords = keywordIds
      }
    }

    // Create the post
    const createdPost = await payload.create({
      collection: 'posts',
      // TODO: Remove this once we have a proper publishing workflow
      data: { ...postData, _status: 'published' },
      overrideAccess: true,
    })

    return {
      success: true,
      post: {
        id: Number(createdPost.id),
        title: createdPost.title,
        slug: createdPost.slug || uniqueSlug,
      },
    }
  } catch (error: any) {
    return {
      success: false,
      error: error.message || 'Unknown error',
    }
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Post Creation API',
    description: 'Unified endpoint for creating single posts or multiple posts in batch',
    usage: {
      singlePost: 'Send a single post object',
      batchPosts: 'Send an object with "posts" array containing multiple posts',
    },
    documentation: '/docs/API/post_creation_enhancement.md',
  })
}

export async function POST(request: NextRequest) {
  // Verify authentication
  const authHeader = request.headers.get('Authorization')
  console.log('authHeader', authHeader)
  if (!authHeader || !authHeader.startsWith('Bearer ') || authHeader.split(' ')[1] !== AUTH_TOKEN) {
    return NextResponse.json(
      {
        success: false,
        error: 'Unauthorized',
      } satisfies z.infer<typeof ErrorResponseSchema>,
      { status: 401 },
    )
  }

  try {
    // Parse request body
    const body = await request.json()
    const parseResult = RequestSchema.safeParse(body)
    console.log(JSON.stringify(parseResult, null, 2))

    if (!parseResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid request format',
          details: parseResult.error.format(),
        } satisfies z.infer<typeof ErrorResponseSchema>,
        { status: 400 },
      )
    }

    const requestData = parseResult.data

    // Get configuration for Lexical conversion
    const config = await configPromise

    // Initialize empty category and keyword caches - we'll populate them as needed
    const categoryMap: Record<string, number> = {}
    const keywordMap: Record<string, number> = {}

    // Check if this is a single post or batch request
    if ('posts' in requestData) {
      // Batch processing
      const { posts } = requestData

      // Prepare batch response
      const response: BatchResponseType = {
        success: true,
        message: 'Posts processing completed',
        totalRequested: posts.length,
        succeeded: 0,
        failed: 0,
        results: [],
      }

      // Process each post
      for (const post of posts) {
        const result = await processSinglePost(post, categoryMap, keywordMap, config)

        if (result.success) {
          response.results.push({
            id: result.post.id,
            title: result.post.title,
            slug: result.post.slug,
            success: true,
          })
          response.succeeded++
        } else {
          response.results.push({
            title: post.title,
            slug: post.slug,
            success: false,
            error: result.error,
          })
          response.failed++
        }
      }

      // Update success status if any failures
      if (response.failed > 0 && response.succeeded === 0) {
        response.success = false
        response.message = 'All posts failed to create'
      } else if (response.failed > 0) {
        response.message = `Processed with some failures: ${response.succeeded} succeeded, ${response.failed} failed`
      } else {
        response.message = `Successfully created ${response.succeeded} posts`
      }

      return NextResponse.json(response, {
        status: response.success ? 200 : 207,
      })
    } else {
      // Single post processing
      const result = await processSinglePost(requestData, categoryMap, keywordMap, config)
      console.log('result', result)

      if (result.success) {
        return NextResponse.json(
          {
            success: true,
            post: result.post,
          } satisfies z.infer<typeof SinglePostResponseSchema>,
          { status: 201 },
        )
      } else {
        return NextResponse.json(
          {
            success: false,
            error: result.error,
          } satisfies z.infer<typeof ErrorResponseSchema>,
          { status: 400 },
        )
      }
    }
  } catch (error: any) {
    console.error('API Error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Server error',
        details: error.message || 'Unknown error occurred',
      } satisfies z.infer<typeof ErrorResponseSchema>,
      { status: 500 },
    )
  }
}
