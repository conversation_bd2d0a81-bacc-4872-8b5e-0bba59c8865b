import type { CollectionConfig } from 'payload'

import { anyone } from '../access/anyone'
import { authenticated } from '../access/authenticated'

export const Keywords: CollectionConfig = {
  slug: 'keywords',
  access: {
    create: authenticated,
    delete: authenticated,
    read: anyone,
    update: authenticated,
  },
  admin: {
    useAsTitle: 'name',
    defaultColumns: ['name', 'usage_count', 'createdAt'],
    description: '關鍵詞管理，用於文章標籤和相關文章推薦',
  },
  fields: [
    {
      name: 'name',
      label: '關鍵詞名稱',
      type: 'text',
      required: true,
      unique: true,
      index: true,
      admin: {
        description: '中文關鍵詞，如：澳門、文化局、經濟多元、中小企',
      },
    },
    {
      name: 'usage_count',
      label: '使用次數',
      type: 'number',
      defaultValue: 0,
      admin: {
        readOnly: true,
        description: '此關鍵詞被使用的文章數量',
      },
    },

    {
      name: 'last_used',
      label: '最後使用時間',
      type: 'date',
      admin: {
        readOnly: true,
        position: 'sidebar',
      },
    },
  ],
  hooks: {
    beforeChange: [
      ({ data }) => {
        // Trim whitespace from keyword name
        if (data.name) {
          data.name = data.name.trim()
        }
        return data
      },
    ],
  },
}
